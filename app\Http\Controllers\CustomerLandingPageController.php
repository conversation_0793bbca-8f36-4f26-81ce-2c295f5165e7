<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;

class CustomerLandingPageController extends Controller
{
    public function index()
    {
        // Ambil kategori dengan produk dan ulasan yang sudah divalidasi
        $categories = Category::with([
            'product' => function ($query) {
                $query->with([
                    'review' => function ($query) {
                        $query->where('status_ulasan', 'Sudah Divalidasi')->with('user');
                    }
                ]);
            }
        ])->get();

        // Ambil produk dengan ID tertentu dan ulasan yang sudah divalidasi
        $productId = 3; // ID produk yang ingin ditampilkan
        $product = Product::with([
            'review' => function ($query) {
                $query->where('status_ulasan', 'Sudah Divalidasi')->with('user');
            }
        ])->find($productId);

        // Jika produk tidak ditemukan, ambil produk pertama atau buat dummy
        if (!$product && Product::count() > 0) {
            $product = Product::with([
                'review' => function ($query) {
                    $query->where('status_ulasan', 'Sudah Divalidasi')->with('user');
                }
            ])->first();
        }

        // Ambil produk terlaris dengan ulasan yang sudah divalidasi
        $produkTerbaik = Product::with([
            'review' => function ($query) {
                $query->where('status_ulasan', 'Sudah Divalidasi')->with('user');
            }
        ])
            ->selectRaw('
                products.id,
                products.nama,
                products.harga,
                products.stok,
                products.gambar_produk,
                products.deskripsi,
                products.category_id,
                COALESCE(SUM(transaction_details.jmlh_pesan), 0) as stok_terjual
            ')
            ->leftJoin('transaction_details', 'products.id', '=', 'transaction_details.product_id')
            ->groupBy('products.id', 'products.nama', 'products.harga', 'products.stok', 'products.gambar_produk', 'products.deskripsi', 'products.category_id')
            ->orderByDesc('stok_terjual')
            ->limit(3)
            ->get();

        // Return the correct view path
        return view('dashboard.landingpage.index', compact('categories', 'produkTerbaik', 'product'));
    }
}
