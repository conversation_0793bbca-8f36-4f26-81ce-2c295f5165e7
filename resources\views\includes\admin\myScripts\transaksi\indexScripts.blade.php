
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

<script>
    $(document).ready(function () {
        $("#dataTable").DataTable({
            scrollX: true, // Scroll horizontal
            scrollY: '50vh', // Scroll vertikal dengan tinggi tertentu (50% viewport height)
            scrollCollapse: true, // Aktifkan scroll jika data kurang dari tinggi yang ditentukan
            responsive: false, // Responsivitas (bisa dinonaktifkan jika ada scrollX/Y)
            language: {
                search: "Cari:",
                lengthMenu: "Tampilkan _MENU_ data per halaman",
                info: "Menampilkan _START_ hingga _END_ dari _TOTAL_ data",
                infoEmpty: "Tidak ada data yang tersedia",
                paginate: {
                    first: "<PERSON>tam<PERSON>",
                    last: "Terakhir",
                    next: "Berikutnya",
                    previous: "Sebelumnya",
                },
            },
        });


    });
</script>
