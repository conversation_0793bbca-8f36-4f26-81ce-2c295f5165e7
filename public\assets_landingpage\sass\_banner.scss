/**************** banner part css start ****************/
.banner_part{
    height: 880px;
    position: relative;
    background-image: url(../img/banner_bg.png);
    background-repeat: no-repeat;
    background-size: 41%;
    background-position: top right;
    @media #{$small_mobile}{
        height: 650px;
        background-image: none;
        background-color: #f0eed4;
    }
    @media #{$large_mobile}{
        height: 650px;
        background-image: none;
        background-color: #f0eed4;
    }
    @media #{$tab_device}{
        height: 700px;
        background-image: none;
        background-color: #f0eed4;
    }
    @media #{$medium_device}{
        background-size: 35%;
        height: 650px;
    }
    &:after{
        width: 100%;
        height: 100%;
        position: absolute;
        content: "";
        bottom: -50px;
        z-index: -1;
        background: url(../img/banner_overlay.png) bottom right no-repeat;
        right: 19%;
        @media #{$small_mobile}{
            display: none;
        }
        @media #{$large_mobile}{
            display: none;        
        }
        @media #{$tab_device}{
            display: none;  
        }
        @media #{$medium_device}{
            right: 10%;
        }
    }
    
    .banner_text{
        display: table;
        width: 100%;
        height: 880px;
        padding-top: 40px;
        .banner_text_iner{
            display: table-cell;
            vertical-align: middle;
            
        }
        @media #{$small_mobile}{
            text-align: center;
            padding-top: 0px;
            height: 650px;
        }
        @media #{$large_mobile}{
            text-align: center;
            padding-top: 0px;
            height: 650px;
        }
        @media #{$tab_device}{
            text-align: center;
            padding-top: 0px;
            height: 700px;
        }
        @media #{$medium_device}{
            height: 650px;
        }
        h5{
            font-size: 24px;
            text-transform: capitalize;
            color: #ff6426;
            font-style: italic;
            font-weight: 400;
            font-family: $font_stack_1;
            margin-bottom: 25px;
        }
        p{
            margin-bottom: 45px;
        }
        h1{
            font-size: 53px;
            font-family: $font_stack_2;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.273;
              
            @media #{$small_mobile}{
                font-size: 27px;
                margin-bottom: 15px;
                line-height: 1.3;
            }
            @media #{$large_mobile}{
                font-size: 40px;
                margin-bottom: 15px;
                line-height: 1.3;
            }
            @media #{$tab_device}{
                font-size: 40px;
                margin-bottom: 15px;
                line-height: 1.3;
            }
            @media #{$medium_device}{
                font-size: 40px;
                margin-bottom: 15px;
                line-height: 1.4;
            }
            span{
                color: $btn_bg;
            }
        }
        .banner_btn_iner{
            position: relative;
            display: inline-block;
            @include transform_time(0.5s);
            border-right: 1px solid transparent;
            &:before{
                width: 60px;
                height: 1px;
                position: absolute;
                content: "";
                top: 0px;
                border-radius: 5px;
                z-index: -1;
                background-color: #2c3033;
                left: 0;
                @include transform_time(0.5s);
            }
            &:after{
                width: 60px;
                height: 1px;
                position: absolute;
                content: "";
                bottom: 0px;
                border-radius: 5px;
                z-index: -1;
                background-color: #2c3033;
                left: 0;
                @include transform_time(0.5s);
            }
            &:hover {
                border-right: 1px solid;
                @include transform_time(0.5s);
                &:before{
                    width: 100%;
                }
                &:after{
                    width: 100%;
                }
                .btn_2 img{
                    padding-right: 15px;
                }
                .btn_2:after{
                    right: 25px;
                    background-color: $btn_bg;
                }
            }
            
        }
        .btn_2{
            padding: 14px 0px 14px 25px;
            border-left: 1px solid #2c3033;
            position: relative;
            @media #{$small_mobile}{
                margin-bottom: 20px;
            }
        }
    }
    
    .video_popup {
        z-index: 99 !important;
        color: #333848;
        font-size: 14px;
        margin-left: 5px;
        display: inline-block;
        font-weight: 500;
        text-transform: capitalize;
          
        span{
            width: 44px;
            height: 44px;
            background-color: rgb(252, 245, 238);
            display: inline-block;
            border-radius: 50%;
            text-align: center;
            line-height: 41px;
            font-size: 14px;
            color: $common_color;
            margin-left: 30px;
            margin-right: 13px;
            padding-left: 5px;
            @include transform_time(0.5s);
            &:hover{
                background-color: rgba(0, 0, 0, 0.1);
            }
            @media #{$small_mobile}{
                margin-left: 0px;
            }
            @media #{$large_mobile}{
            
            }
            @media #{$tab_device}{
            
            }
            @media #{$medium_device}{
            
            }
        }
        @media #{$small_mobile}{
            margin-left: 0px
        } 
    }
}
  
/**************** hero part css end ****************/
