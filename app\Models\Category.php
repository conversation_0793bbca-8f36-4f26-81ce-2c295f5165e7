<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama', // <PERSON><PERSON> kategori produk
    ];

    // <PERSON><PERSON><PERSON> dengan produk
    public function product()
    {
        return $this->hasMany(Product::class, 'category_id', 'id');
    }
}
    