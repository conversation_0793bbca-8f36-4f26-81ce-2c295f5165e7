/**************** about css start ****************/
.about_part{
    position: relative;
    z-index: 1;
    padding: 80px 0px;
    .about_text{
        h5{
            font-family: $font_stack_1;
            font-size: 16px;
            color: #555555;
            margin-bottom: 18px;
        }
        h4{
            margin-bottom: 25px;
            color: $common_color;
            font-size: 24px; 
            font-weight: 300; 
            @media #{$small_mobile}{
                margin-bottom: 20px;
                font-size: 17px;
            }
            @media #{$large_mobile}{
                margin-bottom: 20px;
                font-size: 17px;
            }
            @media #{$tab_device}{
                margin-bottom: 20px;
                font-size: 17px;
            }
            @media #{$medium_device}{
                margin-bottom: 20px;
                font-size: 17px;
            }       
        }
        h2{
            line-height: 1.25;
            margin-bottom: 25px;
            font-size: 40px;
            @media #{$small_mobile}{
                margin-bottom: 15px;
            }
            @media #{$large_mobile}{
                margin-bottom: 15px;
            }
            @media #{$tab_device}{
                font-size: 25px;  
                margin-bottom: 15px;
            }
            @media #{$medium_device}{
                font-size: 30px;  
                margin-bottom: 15px;
            }
        }
        p{
            margin-top: 13px;
        }
        .btn_3{
            margin-top: 48px;
        }
    }
}
.about_bg{
    position: relative;
    z-index: 1;
    &:after{
        width: 100%;
        height: 100%;
        position: absolute;
        content: "";
        bottom: 133px;
        z-index: -1;
        background: url(../img/about_overlay.png) bottom right no-repeat;
        background-size: 15% 60%;
        right: 0px;
        @media #{$small_mobile}{
            display: none;
        }
        @media #{$large_mobile}{
            display: none;
        }
        @media #{$tab_device}{
            display: none;
        }
        @media #{$medium_device}{
            background-size: 15% 50%;
        }
    }
}