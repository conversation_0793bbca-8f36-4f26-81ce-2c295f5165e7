@extends('layout.admin.index')

@section('title', 'Edit Peran User | RFF')

@section('content')
    <div class="container">
        <div class="page-inner">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="d-flex justify-content-start align-items-center pt-3 pb-3">
                        <h3 class="fw-bold">Edit Peran User</h3>
                    </div>
                    <div class="card">
                        <div class="card-header text-center">
                            <h4>Form Edit Peran User</h4>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('dashboard.roles.update', $role->id) }}">
                                @csrf
                                @method('PUT')
                                <div class="row">
                                    <!-- <PERSON>a <PERSON> -->
                                    <div class="col-md-12 mb-3">
                                        <label for="name"><PERSON><PERSON></label>
                                        <input type="text" class="form-control" name="name" id="name"
                                            placeholder="<PERSON><PERSON>" value="{{ $role->name }}" required />
                                    </div>

                                    <!-- Hak Akses -->
                                    <div class="col-md-12 mb-3">
                                        <label for="permissions">Hak Akses</label>
                                        <select id="permissions" name="permissions[]" class="selectpicker" multiple
                                            data-actions-box="true" data-live-search="true" title="Pilih Hak Akses">
                                            @foreach ($permissions as $permission)
                                                <option value="{{ $permission->id }}" 
                                                    {{ $role->permissions->contains($permission->id) ? 'selected' : '' }}>
                                                    {{ $permission->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <!-- Tabel Permissions -->
                                    <div class="col-md-12 mt-3">
                                        <h5>Daftar Hak Akses Terpilih:</h5>
                                        <table class="table table-bordered" id="permissionsTable">
                                            <thead>
                                                <tr>
                                                    <th>No</th>
                                                    <th>Nama Hak Akses</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($role->permissions as $index => $permission)
                                                    <tr data-id="{{ $permission->id }}">
                                                        <td>{{ $index + 1 }}</td>
                                                        <td>{{ $permission->name }}</td>
                                                        <td>
                                                            <button type="button" class="btn btn-danger btn-sm remove-permission">Hapus</button>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Tombol Simpan -->
                                    <div class="col-md-12 text-center mt-4">
                                        <button type="submit" class="btn btn-primary mx-2">Simpan Perubahan</button>
                                        <a href="{{ route('dashboard.roles.index') }}"
                                            class="btn btn-danger mx-2">Kembali</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 
 @push('scripts')
    @include('includes.admin.myScripts.peran_user.create')
@endpush

@endsection
