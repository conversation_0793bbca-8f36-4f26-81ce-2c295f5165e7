select[multiple] {
    height: auto;
    min-height: 120px;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    background-color: #fff;
    box-shadow: inset 0 1px 2px rgb(0 0 0 / 8%);
}

select[multiple]:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}


.dataTables_wrapper {
    overflow-x: auto; /* Untuk scroll horizontal */
}

table.dataTable {
    width: 100%; /* Pastikan tabel menyesuaikan ruang yang ada */
}

.table-responsive {
    overflow-y: auto; /* Pastikan scroll vertikal aktif */
}
