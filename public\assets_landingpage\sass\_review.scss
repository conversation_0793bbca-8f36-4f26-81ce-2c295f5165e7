/**************** review_part css start ****************/
.review_part{
  .client_img{
    width: 200px;
    float: left;
    padding-top: 22px;
    @media #{$small_mobile}{
     display: block;
     width: 100%;
     padding-top: 0px;

    }
    @media #{$large_mobile}{
      display: block;
      width: 100%;
      padding-top: 0px;
    }
    @media #{$tab_device}{
      display: block;
      width: 100%;
      padding-top: 0px;
    }
    @media #{$medium_device}{
    
    }
    img{
      max-width: 140px;
      max-height: 140px;
    }
  }
  .client_review_part{
    position: relative;
    z-index: 1;
    &:after{
      position: absolute;
      content: "";
      left: 200px;
      top: 0px;
      width: 1px;
      height: 100%;
      border: 1px solid #d9d9d9;
      @media #{$small_mobile}{
        display: none;
      }
      @media #{$large_mobile}{
        display: none;
      
      }
      @media #{$tab_device}{
        display: none;
      }
      @media #{$medium_device}{
      
      }
    }
    &:before{
      position: absolute;
      content: "";
      left: 60%;
    top: 13%;
    width: 174px;
    height: 139px;

      background-image: url(../img/Quote.png);
      background-size: cover;
      @media #{$small_mobile}{
        display: none;
      }
      @media #{$large_mobile}{
        display: none;
      
      }
      @media #{$tab_device}{
        display: none;
      }
      @media #{$medium_device}{
      
      }
    }
  }
  .client_review_single {
    .client_review_text{
      padding-left: 50px;
      @media #{$small_mobile}{
        padding-left: 0px;
       }
       @media #{$large_mobile}{
        padding-left: 0px;
       }
       @media #{$tab_device}{
        padding-left: 0px
       }
       @media #{$medium_device}{
       
       }
      p{
        font-size: 20px;
        font-family: $font_stack_1;
        color: rgb(85, 85, 85);
        font-style: italic;
        line-height: 1.6;
        @media #{$small_mobile}{
          font-size: 16px;
         }
         @media #{$large_mobile}{
          font-size: 16px;
         }
         @media #{$tab_device}{
          font-size: 16px;
         }
         @media #{$medium_device}{
         
         }
      }
      h4{
        margin-top: 30px;
        font-size: 24px;
        font-weight: 700;
        color: #2c3033;
        @media #{$small_mobile}{
          margin-top: 20px;
          font-size: 20px;
        }
        @media #{$large_mobile}{
          margin-top: 20px;    
          font-size: 20px;
        }
        @media #{$tab_device}{
          margin-top: 20px;    
          font-size: 20px;
        }
        @media #{$medium_device}{
        
        }
        span{
          font-family: $font_stack_2;
          font-size: 14px;
          color: #555555;
        }
      }
    }
  }
  .owl-dots {
    padding-top: 15px;
    padding-left: 240px;
    @media #{$small_mobile}{
      padding-left: 0px;
     }
     @media #{$large_mobile}{
      padding-left: 0px;
     }
     @media #{$tab_device}{
      padding-left: 0px;
     }
     @media #{$medium_device}{
     
     }
    button.owl-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin: 0 15px;
        padding: 4px !important;
        position: relative;
        z-index: 1;
        background-color: #fcf5ee;
        &:after{
          position: absolute;
          content: "";
          left: -4px;
          top: -4px;
          width: 16px;
          height: 16px;
          border: 1px solid #ddd;
          border-radius: 50%;
        }
        
        &.active {
          background-color: rgb(255, 160, 122);
          border-radius: 50%;
          
        }
        &:focus {
            outline: none;
        }
    }
}
}