<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Pastikan symbolic link storage sudah dibuat
        if (!file_exists(public_path('storage'))) {
            try {
                \Artisan::call('storage:link');
            } catch (\Exception $e) {
                // Log error jika terjadi masalah
                \Log::error('Gagal membuat symbolic link: ' . $e->getMessage());
            }
        }

        // Pastikan direktori uploads/transaksi ada
        $uploadPath = storage_path('app/public/uploads/transaksi');
        if (!file_exists($uploadPath)) {
            try {
                mkdir($uploadPath, 0755, true);
            } catch (\Exception $e) {
                // Log error jika terjadi masalah
                \Log::error('Gagal membuat direktori uploads/transaksi: ' . $e->getMessage());
            }
        }
    }
}
