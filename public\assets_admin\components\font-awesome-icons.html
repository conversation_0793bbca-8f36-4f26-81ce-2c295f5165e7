<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Font Awesome - Kaiadmin Bootstrap 5 Admin Dashboard</title>
    <meta
      content="width=device-width, initial-scale=1.0, shrink-to-fit=no"
      name="viewport"
    />
    <link
      rel="icon"
      href="../assets/img/kaiadmin/favicon.ico"
      type="image/x-icon"
    />

    <!-- Fonts and icons -->
    <script src="../assets/js/plugin/webfont/webfont.min.js"></script>
    <script>
      WebFont.load({
        google: { families: ["Public Sans:300,400,500,600,700"] },
        custom: {
          families: [
            "Font Awesome 5 Solid",
            "Font Awesome 5 Regular",
            "Font Awesome 5 Brands",
            "simple-line-icons",
          ],
          urls: ["../assets/css/fonts.min.css"],
        },
        active: function () {
          sessionStorage.fonts = true;
        },
      });
    </script>

    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css" />
    <link rel="stylesheet" href="../assets/css/plugins.min.css" />
    <link rel="stylesheet" href="../assets/css/kaiadmin.min.css" />

    <!-- CSS Just for demo purpose, don't include it in your project -->
    <link rel="stylesheet" href="../assets/css/demo.css" />
  </head>
  <body>
    <div class="wrapper">
      <!-- Sidebar -->
      <div class="sidebar" data-background-color="dark">
        <div class="sidebar-logo">
          <!-- Logo Header -->
          <div class="logo-header" data-background-color="dark">
            <a href="../index.html" class="logo">
              <img
                src="../assets/img/kaiadmin/logo_light.svg"
                alt="navbar brand"
                class="navbar-brand"
                height="20"
              />
            </a>
            <div class="nav-toggle">
              <button class="btn btn-toggle toggle-sidebar">
                <i class="gg-menu-right"></i>
              </button>
              <button class="btn btn-toggle sidenav-toggler">
                <i class="gg-menu-left"></i>
              </button>
            </div>
            <button class="topbar-toggler more">
              <i class="gg-more-vertical-alt"></i>
            </button>
          </div>
          <!-- End Logo Header -->
        </div>
        <div class="sidebar-wrapper scrollbar scrollbar-inner">
          <div class="sidebar-content">
            <ul class="nav nav-secondary">
              <li class="nav-item">
                <a
                  data-bs-toggle="collapse"
                  href="#dashboard"
                  class="collapsed"
                  aria-expanded="false"
                >
                  <i class="fas fa-home"></i>
                  <p>Dashboard</p>
                  <span class="caret"></span>
                </a>
                <div class="collapse" id="dashboard">
                  <ul class="nav nav-collapse">
                    <li>
                      <a href="../../demo1/index.html">
                        <span class="sub-item">Dashboard 1</span>
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
              <li class="nav-section">
                <span class="sidebar-mini-icon">
                  <i class="fa fa-ellipsis-h"></i>
                </span>
                <h4 class="text-section">Components</h4>
              </li>
              <li class="nav-item active submenu">
                <a data-bs-toggle="collapse" href="#base">
                  <i class="fas fa-layer-group"></i>
                  <p>Base</p>
                  <span class="caret"></span>
                </a>
                <div class="collapse show" id="base">
                  <ul class="nav nav-collapse">
                    <li>
                      <a href="../components/avatars.html">
                        <span class="sub-item">Avatars</span>
                      </a>
                    </li>
                    <li>
                      <a href="../components/buttons.html">
                        <span class="sub-item">Buttons</span>
                      </a>
                    </li>
                    <li>
                      <a href="../components/gridsystem.html">
                        <span class="sub-item">Grid System</span>
                      </a>
                    </li>
                    <li>
                      <a href="../components/panels.html">
                        <span class="sub-item">Panels</span>
                      </a>
                    </li>
                    <li>
                      <a href="../components/notifications.html">
                        <span class="sub-item">Notifications</span>
                      </a>
                    </li>
                    <li>
                      <a href="../components/sweetalert.html">
                        <span class="sub-item">Sweet Alert</span>
                      </a>
                    </li>
                    <li class="active">
                      <a href="../components/font-awesome-icons.html">
                        <span class="sub-item">Font Awesome Icons</span>
                      </a>
                    </li>
                    <li>
                      <a href="../components/simple-line-icons.html">
                        <span class="sub-item">Simple Line Icons</span>
                      </a>
                    </li>
                    <li>
                      <a href="../components/typography.html">
                        <span class="sub-item">Typography</span>
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
              <li class="nav-item">
                <a data-bs-toggle="collapse" href="#sidebarLayouts">
                  <i class="fas fa-th-list"></i>
                  <p>Sidebar Layouts</p>
                  <span class="caret"></span>
                </a>
                <div class="collapse" id="sidebarLayouts">
                  <ul class="nav nav-collapse">
                    <li>
                      <a href="../sidebar-style-2.html">
                        <span class="sub-item">Sidebar Style 2</span>
                      </a>
                    </li>
                    <li>
                      <a href="../icon-menu.html">
                        <span class="sub-item">Icon Menu</span>
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
              <li class="nav-item">
                <a data-bs-toggle="collapse" href="#forms">
                  <i class="fas fa-pen-square"></i>
                  <p>Forms</p>
                  <span class="caret"></span>
                </a>
                <div class="collapse" id="forms">
                  <ul class="nav nav-collapse">
                    <li>
                      <a href="../forms/forms.html">
                        <span class="sub-item">Basic Form</span>
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
              <li class="nav-item">
                <a data-bs-toggle="collapse" href="#tables">
                  <i class="fas fa-table"></i>
                  <p>Tables</p>
                  <span class="caret"></span>
                </a>
                <div class="collapse" id="tables">
                  <ul class="nav nav-collapse">
                    <li>
                      <a href="../tables/tables.html">
                        <span class="sub-item">Basic Table</span>
                      </a>
                    </li>
                    <li>
                      <a href="../tables/datatables.html">
                        <span class="sub-item">Datatables</span>
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
              <li class="nav-item">
                <a data-bs-toggle="collapse" href="#maps">
                  <i class="fas fa-map-marker-alt"></i>
                  <p>Maps</p>
                  <span class="caret"></span>
                </a>
                <div class="collapse" id="maps">
                  <ul class="nav nav-collapse">
                    <li>
                      <a href="../maps/googlemaps.html">
                        <span class="sub-item">Google Maps</span>
                      </a>
                    </li>
                    <li>
                      <a href="../maps/jsvectormap.html">
                        <span class="sub-item">Jsvectormap</span>
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
              <li class="nav-item">
                <a data-bs-toggle="collapse" href="#charts">
                  <i class="far fa-chart-bar"></i>
                  <p>Charts</p>
                  <span class="caret"></span>
                </a>
                <div class="collapse" id="charts">
                  <ul class="nav nav-collapse">
                    <li>
                      <a href="../charts/charts.html">
                        <span class="sub-item">Chart Js</span>
                      </a>
                    </li>
                    <li>
                      <a href="../charts/sparkline.html">
                        <span class="sub-item">Sparkline</span>
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
              <li class="nav-item">
                <a href="../widgets.html">
                  <i class="fas fa-desktop"></i>
                  <p>Widgets</p>
                  <span class="badge badge-success">4</span>
                </a>
              </li>
              <li class="nav-item">
                <a href="../../../documentation/index.html">
                  <i class="fas fa-file"></i>
                  <p>Documentation</p>
                  <span class="badge badge-secondary">1</span>
                </a>
              </li>
              <li class="nav-item">
                <a data-bs-toggle="collapse" href="#submenu">
                  <i class="fas fa-bars"></i>
                  <p>Menu Levels</p>
                  <span class="caret"></span>
                </a>
                <div class="collapse" id="submenu">
                  <ul class="nav nav-collapse">
                    <li>
                      <a data-bs-toggle="collapse" href="#subnav1">
                        <span class="sub-item">Level 1</span>
                        <span class="caret"></span>
                      </a>
                      <div class="collapse" id="subnav1">
                        <ul class="nav nav-collapse subnav">
                          <li>
                            <a href="#">
                              <span class="sub-item">Level 2</span>
                            </a>
                          </li>
                          <li>
                            <a href="#">
                              <span class="sub-item">Level 2</span>
                            </a>
                          </li>
                        </ul>
                      </div>
                    </li>
                    <li>
                      <a data-bs-toggle="collapse" href="#subnav2">
                        <span class="sub-item">Level 1</span>
                        <span class="caret"></span>
                      </a>
                      <div class="collapse" id="subnav2">
                        <ul class="nav nav-collapse subnav">
                          <li>
                            <a href="#">
                              <span class="sub-item">Level 2</span>
                            </a>
                          </li>
                        </ul>
                      </div>
                    </li>
                    <li>
                      <a href="#">
                        <span class="sub-item">Level 1</span>
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <!-- End Sidebar -->

      <div class="main-panel">
        <div class="main-header">
          <div class="main-header-logo">
            <!-- Logo Header -->
            <div class="logo-header" data-background-color="dark">
              <a href="../index.html" class="logo">
                <img
                  src="../assets/img/kaiadmin/logo_light.svg"
                  alt="navbar brand"
                  class="navbar-brand"
                  height="20"
                />
              </a>
              <div class="nav-toggle">
                <button class="btn btn-toggle toggle-sidebar">
                  <i class="gg-menu-right"></i>
                </button>
                <button class="btn btn-toggle sidenav-toggler">
                  <i class="gg-menu-left"></i>
                </button>
              </div>
              <button class="topbar-toggler more">
                <i class="gg-more-vertical-alt"></i>
              </button>
            </div>
            <!-- End Logo Header -->
          </div>
          <!-- Navbar Header -->
          <nav
            class="navbar navbar-header navbar-header-transparent navbar-expand-lg border-bottom"
          >
            <div class="container-fluid">
              <nav
                class="navbar navbar-header-left navbar-expand-lg navbar-form nav-search p-0 d-none d-lg-flex"
              >
                <div class="input-group">
                  <div class="input-group-prepend">
                    <button type="submit" class="btn btn-search pe-1">
                      <i class="fa fa-search search-icon"></i>
                    </button>
                  </div>
                  <input
                    type="text"
                    placeholder="Search ..."
                    class="form-control"
                  />
                </div>
              </nav>

              <ul class="navbar-nav topbar-nav ms-md-auto align-items-center">
                <li
                  class="nav-item topbar-icon dropdown hidden-caret d-flex d-lg-none"
                >
                  <a
                    class="nav-link dropdown-toggle"
                    data-bs-toggle="dropdown"
                    href="#"
                    role="button"
                    aria-expanded="false"
                    aria-haspopup="true"
                  >
                    <i class="fa fa-search"></i>
                  </a>
                  <ul class="dropdown-menu dropdown-search animated fadeIn">
                    <form class="navbar-left navbar-form nav-search">
                      <div class="input-group">
                        <input
                          type="text"
                          placeholder="Search ..."
                          class="form-control"
                        />
                      </div>
                    </form>
                  </ul>
                </li>
                <li class="nav-item topbar-icon dropdown hidden-caret">
                  <a
                    class="nav-link dropdown-toggle"
                    href="#"
                    id="messageDropdown"
                    role="button"
                    data-bs-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    <i class="fa fa-envelope"></i>
                  </a>
                  <ul
                    class="dropdown-menu messages-notif-box animated fadeIn"
                    aria-labelledby="messageDropdown"
                  >
                    <li>
                      <div
                        class="dropdown-title d-flex justify-content-between align-items-center"
                      >
                        Messages
                        <a href="#" class="small">Mark all as read</a>
                      </div>
                    </li>
                    <li>
                      <div class="message-notif-scroll scrollbar-outer">
                        <div class="notif-center">
                          <a href="#">
                            <div class="notif-img">
                              <img
                                src="../assets/img/jm_denis.jpg"
                                alt="Img Profile"
                              />
                            </div>
                            <div class="notif-content">
                              <span class="subject">Jimmy Denis</span>
                              <span class="block"> How are you ? </span>
                              <span class="time">5 minutes ago</span>
                            </div>
                          </a>
                          <a href="#">
                            <div class="notif-img">
                              <img
                                src="../assets/img/chadengle.jpg"
                                alt="Img Profile"
                              />
                            </div>
                            <div class="notif-content">
                              <span class="subject">Chad</span>
                              <span class="block"> Ok, Thanks ! </span>
                              <span class="time">12 minutes ago</span>
                            </div>
                          </a>
                          <a href="#">
                            <div class="notif-img">
                              <img
                                src="../assets/img/mlane.jpg"
                                alt="Img Profile"
                              />
                            </div>
                            <div class="notif-content">
                              <span class="subject">Jhon Doe</span>
                              <span class="block">
                                Ready for the meeting today...
                              </span>
                              <span class="time">12 minutes ago</span>
                            </div>
                          </a>
                          <a href="#">
                            <div class="notif-img">
                              <img
                                src="../assets/img/talha.jpg"
                                alt="Img Profile"
                              />
                            </div>
                            <div class="notif-content">
                              <span class="subject">Talha</span>
                              <span class="block"> Hi, Apa Kabar ? </span>
                              <span class="time">17 minutes ago</span>
                            </div>
                          </a>
                        </div>
                      </div>
                    </li>
                    <li>
                      <a class="see-all" href="javascript:void(0);"
                        >See all messages<i class="fa fa-angle-right"></i>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="nav-item topbar-icon dropdown hidden-caret">
                  <a
                    class="nav-link dropdown-toggle"
                    href="#"
                    id="notifDropdown"
                    role="button"
                    data-bs-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    <i class="fa fa-bell"></i>
                    <span class="notification">4</span>
                  </a>
                  <ul
                    class="dropdown-menu notif-box animated fadeIn"
                    aria-labelledby="notifDropdown"
                  >
                    <li>
                      <div class="dropdown-title">
                        You have 4 new notification
                      </div>
                    </li>
                    <li>
                      <div class="notif-scroll scrollbar-outer">
                        <div class="notif-center">
                          <a href="#">
                            <div class="notif-icon notif-primary">
                              <i class="fa fa-user-plus"></i>
                            </div>
                            <div class="notif-content">
                              <span class="block"> New user registered </span>
                              <span class="time">5 minutes ago</span>
                            </div>
                          </a>
                          <a href="#">
                            <div class="notif-icon notif-success">
                              <i class="fa fa-comment"></i>
                            </div>
                            <div class="notif-content">
                              <span class="block">
                                Rahmad commented on Admin
                              </span>
                              <span class="time">12 minutes ago</span>
                            </div>
                          </a>
                          <a href="#">
                            <div class="notif-img">
                              <img
                                src="../assets/img/profile2.jpg"
                                alt="Img Profile"
                              />
                            </div>
                            <div class="notif-content">
                              <span class="block">
                                Reza send messages to you
                              </span>
                              <span class="time">12 minutes ago</span>
                            </div>
                          </a>
                          <a href="#">
                            <div class="notif-icon notif-danger">
                              <i class="fa fa-heart"></i>
                            </div>
                            <div class="notif-content">
                              <span class="block"> Farrah liked Admin </span>
                              <span class="time">17 minutes ago</span>
                            </div>
                          </a>
                        </div>
                      </div>
                    </li>
                    <li>
                      <a class="see-all" href="javascript:void(0);"
                        >See all notifications<i class="fa fa-angle-right"></i>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="nav-item topbar-icon dropdown hidden-caret">
                  <a
                    class="nav-link"
                    data-bs-toggle="dropdown"
                    href="#"
                    aria-expanded="false"
                  >
                    <i class="fas fa-layer-group"></i>
                  </a>
                  <div class="dropdown-menu quick-actions animated fadeIn">
                    <div class="quick-actions-header">
                      <span class="title mb-1">Quick Actions</span>
                      <span class="subtitle op-7">Shortcuts</span>
                    </div>
                    <div class="quick-actions-scroll scrollbar-outer">
                      <div class="quick-actions-items">
                        <div class="row m-0">
                          <a class="col-6 col-md-4 p-0" href="#">
                            <div class="quick-actions-item">
                              <div class="avatar-item bg-danger rounded-circle">
                                <i class="far fa-calendar-alt"></i>
                              </div>
                              <span class="text">Calendar</span>
                            </div>
                          </a>
                          <a class="col-6 col-md-4 p-0" href="#">
                            <div class="quick-actions-item">
                              <div
                                class="avatar-item bg-warning rounded-circle"
                              >
                                <i class="fas fa-map"></i>
                              </div>
                              <span class="text">Maps</span>
                            </div>
                          </a>
                          <a class="col-6 col-md-4 p-0" href="#">
                            <div class="quick-actions-item">
                              <div class="avatar-item bg-info rounded-circle">
                                <i class="fas fa-file-excel"></i>
                              </div>
                              <span class="text">Reports</span>
                            </div>
                          </a>
                          <a class="col-6 col-md-4 p-0" href="#">
                            <div class="quick-actions-item">
                              <div
                                class="avatar-item bg-success rounded-circle"
                              >
                                <i class="fas fa-envelope"></i>
                              </div>
                              <span class="text">Emails</span>
                            </div>
                          </a>
                          <a class="col-6 col-md-4 p-0" href="#">
                            <div class="quick-actions-item">
                              <div
                                class="avatar-item bg-primary rounded-circle"
                              >
                                <i class="fas fa-file-invoice-dollar"></i>
                              </div>
                              <span class="text">Invoice</span>
                            </div>
                          </a>
                          <a class="col-6 col-md-4 p-0" href="#">
                            <div class="quick-actions-item">
                              <div
                                class="avatar-item bg-secondary rounded-circle"
                              >
                                <i class="fas fa-credit-card"></i>
                              </div>
                              <span class="text">Payments</span>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>

                <li class="nav-item topbar-user dropdown hidden-caret">
                  <a
                    class="dropdown-toggle profile-pic"
                    data-bs-toggle="dropdown"
                    href="#"
                    aria-expanded="false"
                  >
                    <div class="avatar-sm">
                      <img
                        src="../assets/img/profile.jpg"
                        alt="..."
                        class="avatar-img rounded-circle"
                      />
                    </div>
                    <span class="profile-username">
                      <span class="op-7">Hi,</span>
                      <span class="fw-bold">Hizrian</span>
                    </span>
                  </a>
                  <ul class="dropdown-menu dropdown-user animated fadeIn">
                    <div class="dropdown-user-scroll scrollbar-outer">
                      <li>
                        <div class="user-box">
                          <div class="avatar-lg">
                            <img
                              src="../assets/img/profile.jpg"
                              alt="image profile"
                              class="avatar-img rounded"
                            />
                          </div>
                          <div class="u-text">
                            <h4>Hizrian</h4>
                            <p class="text-muted"><EMAIL></p>
                            <a
                              href="profile.html"
                              class="btn btn-xs btn-secondary btn-sm"
                              >View Profile</a
                            >
                          </div>
                        </div>
                      </li>
                      <li>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#">My Profile</a>
                        <a class="dropdown-item" href="#">My Balance</a>
                        <a class="dropdown-item" href="#">Inbox</a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#">Account Setting</a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#">Logout</a>
                      </li>
                    </div>
                  </ul>
                </li>
              </ul>
            </div>
          </nav>
          <!-- End Navbar -->
        </div>

        <div class="container">
          <div class="page-inner">
            <div class="page-header">
              <h3 class="fw-bold mb-3">Icons</h3>
              <ul class="breadcrumbs mb-3">
                <li class="nav-home">
                  <a href="#">
                    <i class="icon-home"></i>
                  </a>
                </li>
                <li class="separator">
                  <i class="icon-arrow-right"></i>
                </li>
                <li class="nav-item">
                  <a href="#">Base</a>
                </li>
                <li class="separator">
                  <i class="icon-arrow-right"></i>
                </li>
                <li class="nav-item">
                  <a href="#">Line Awesome</a>
                </li>
              </ul>
            </div>
            <div class="row">
              <div class="col-md-12">
                <div class="card">
                  <div class="card-header">
                    <div class="card-title">Font Awesome Icons</div>
                    <div class="card-category">
                      Modern Font Icon from
                      <a class="link" href="https://fontawesome.com"
                        >Font Awesome</a
                      >
                    </div>
                  </div>
                  <div class="card-body">
                    <div id="row-demo-icon" class="row"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <footer class="footer">
          <div class="container-fluid d-flex justify-content-between">
            <nav class="pull-left">
              <ul class="nav">
                <li class="nav-item">
                  <a class="nav-link" href="http://www.themekita.com">
                    ThemeKita
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="#"> Help </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="#"> Licenses </a>
                </li>
              </ul>
            </nav>
            <div class="copyright">
              2024, made with <i class="fa fa-heart heart text-danger"></i> by
              <a href="http://www.themekita.com">ThemeKita</a>
            </div>
            <div>
              Distributed by
              <a target="_blank" href="https://themewagon.com/">ThemeWagon</a>.
            </div>
          </div>
        </footer>
      </div>

      <!-- Custom template | don't include it in your project! -->
      <div class="custom-template">
        <div class="title">Settings</div>
        <div class="custom-content">
          <div class="switcher">
            <div class="switch-block">
              <h4>Logo Header</h4>
              <div class="btnSwitch">
                <button
                  type="button"
                  class="selected changeLogoHeaderColor"
                  data-color="dark"
                ></button>
                <button
                  type="button"
                  class="selected changeLogoHeaderColor"
                  data-color="blue"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="purple"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="light-blue"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="green"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="orange"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="red"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="white"
                ></button>
                <br />
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="dark2"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="blue2"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="purple2"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="light-blue2"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="green2"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="orange2"
                ></button>
                <button
                  type="button"
                  class="changeLogoHeaderColor"
                  data-color="red2"
                ></button>
              </div>
            </div>
            <div class="switch-block">
              <h4>Navbar Header</h4>
              <div class="btnSwitch">
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="dark"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="blue"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="purple"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="light-blue"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="green"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="orange"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="red"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="white"
                ></button>
                <br />
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="dark2"
                ></button>
                <button
                  type="button"
                  class="selected changeTopBarColor"
                  data-color="blue2"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="purple2"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="light-blue2"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="green2"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="orange2"
                ></button>
                <button
                  type="button"
                  class="changeTopBarColor"
                  data-color="red2"
                ></button>
              </div>
            </div>
            <div class="switch-block">
              <h4>Sidebar</h4>
              <div class="btnSwitch">
                <button
                  type="button"
                  class="selected changeSideBarColor"
                  data-color="white"
                ></button>
                <button
                  type="button"
                  class="changeSideBarColor"
                  data-color="dark"
                ></button>
                <button
                  type="button"
                  class="changeSideBarColor"
                  data-color="dark2"
                ></button>
              </div>
            </div>
          </div>
        </div>
        <div class="custom-toggle">
          <i class="icon-settings"></i>
        </div>
      </div>
      <!-- End Custom template -->
    </div>
    <!--   Core JS Files   -->
    <script src="../assets/js/core/jquery-3.7.1.min.js"></script>
    <script src="../assets/js/core/popper.min.js"></script>
    <script src="../assets/js/core/bootstrap.min.js"></script>

    <!-- jQuery Scrollbar -->
    <script src="../assets/js/plugin/jquery-scrollbar/jquery.scrollbar.min.js"></script>
    <!-- Moment JS -->
    <script src="../assets/js/plugin/moment/moment.min.js"></script>

    <!-- Chart JS -->
    <script src="../assets/js/plugin/chart.js/chart.min.js"></script>

    <!-- jQuery Sparkline -->
    <script src="../assets/js/plugin/jquery.sparkline/jquery.sparkline.min.js"></script>

    <!-- Chart Circle -->
    <script src="../assets/js/plugin/chart-circle/circles.min.js"></script>

    <!-- Datatables -->
    <script src="../assets/js/plugin/datatables/datatables.min.js"></script>

    <!-- Bootstrap Notify -->
    <script src="../assets/js/plugin/bootstrap-notify/bootstrap-notify.min.js"></script>

    <!-- jQuery Vector Maps -->
    <script src="../assets/js/plugin/jsvectormap/jsvectormap.min.js"></script>
    <script src="../assets/js/plugin/jsvectormap/world.js"></script>

    <!-- Sweet Alert -->
    <script src="../assets/js/plugin/sweetalert/sweetalert.min.js"></script>

    <!-- Kaiadmin JS -->
    <script src="../assets/js/kaiadmin.min.js"></script>

    <!-- Kaiadmin DEMO methods, don't include it in your project! -->
    <script src="../assets/js/setting-demo2.js"></script>

    <script>
      var iconClass = [
        "fas fa-address-book",
        "fas fa-address-card",
        "fas fa-adjust",
        "fas fa-air-freshener",
        "fas fa-align-center",
        "fas fa-align-justify",
        "fas fa-align-left",
        "fas fa-align-right",
        "fas fa-allergies",
        "fas fa-ambulance",
        "fas fa-american-sign-language-interpreting",
        "fas fa-anchor",
        "fas fa-angle-double-down",
        "fas fa-angle-double-left",
        "fas fa-angle-double-right",
        "fas fa-angle-double-up",
        "fas fa-angle-down",
        "fas fa-angle-left",
        "fas fa-angle-right",
        "fas fa-angle-up",
        "fas fa-angry",
        "fas fa-apple-alt",
        "fas fa-archive",
        "fas fa-archway",
        "fas fa-arrow-alt-circle-down",
        "fas fa-arrow-alt-circle-left",
        "fas fa-arrow-alt-circle-right",
        "fas fa-arrow-alt-circle-up",
        "fas fa-arrow-circle-down",
        "fas fa-arrow-circle-left",
        "fas fa-arrow-circle-right",
        "fas fa-arrow-circle-up",
        "fas fa-arrow-down",
        "fas fa-arrow-left",
        "fas fa-arrow-right",
        "fas fa-arrow-up",
        "fas fa-arrows-alt",
        "fas fa-arrows-alt-h",
        "fas fa-arrows-alt-v",
        "fas fa-assistive-listening-systems",
        "fas fa-asterisk",
        "fas fa-at",
        "fas fa-atlas",
        "fas fa-atom",
        "fas fa-audio-description",
        "fas fa-award",
        "fas fa-backspace",
        "fas fa-backward",
        "fas fa-balance-scale",
        "fas fa-ban",
        "fas fa-band-aid",
        "fas fa-barcode",
        "fas fa-bars",
        "fas fa-baseball-ball",
        "fas fa-basketball-ball",
        "fas fa-bath",
        "fas fa-battery-empty",
        "fas fa-battery-full",
        "fas fa-battery-half",
        "fas fa-battery-quarter",
        "fas fa-battery-three-quarters",
        "fas fa-bed",
        "fas fa-beer",
        "fas fa-bell",
        "fas fa-bell-slash",
        "fas fa-bezier-curve",
        "fas fa-bicycle",
        "fas fa-binoculars",
        "fas fa-birthday-cake",
        "fas fa-blender",
        "fas fa-blind",
        "fas fa-bold",
        "fas fa-bolt",
        "fas fa-bomb",
        "fas fa-bone",
        "fas fa-bong",
        "fas fa-book",
        "fas fa-book-open",
        "fas fa-book-reader",
        "fas fa-bookmark",
        "fas fa-bowling-ball",
        "fas fa-box",
        "fas fa-box-open",
        "fas fa-boxes",
        "fas fa-braille",
        "fas fa-brain",
        "fas fa-briefcase",
        "fas fa-briefcase-medical",
        "fas fa-broadcast-tower",
        "fas fa-broom",
        "fas fa-brush",
        "fas fa-bug",
        "fas fa-building",
        "fas fa-bullhorn",
        "fas fa-bullseye",
        "fas fa-burn",
        "fas fa-bus",
        "fas fa-bus-alt",
        "fas fa-calculator",
        "fas fa-calendar",
        "fas fa-calendar-alt",
        "fas fa-calendar-check",
        "fas fa-calendar-minus",
        "fas fa-calendar-plus",
        "fas fa-calendar-times",
        "fas fa-camera",
        "fas fa-camera-retro",
        "fas fa-cannabis",
        "fas fa-capsules",
        "fas fa-car",
        "fas fa-car-alt",
        "fas fa-car-battery",
        "fas fa-car-crash",
        "fas fa-car-side",
        "fas fa-caret-down",
        "fas fa-caret-left",
        "fas fa-caret-right",
        "fas fa-caret-square-down",
        "fas fa-caret-square-left",
        "fas fa-caret-square-right",
        "fas fa-caret-square-up",
        "fas fa-caret-up",
        "fas fa-cart-arrow-down",
        "fas fa-cart-plus",
        "fas fa-certificate",
        "fas fa-chalkboard",
        "fas fa-chalkboard-teacher",
        "fas fa-charging-station",
        "fas fa-chart-area",
        "fas fa-chart-bar",
        "fas fa-chart-line",
        "fas fa-chart-pie",
        "fas fa-check",
        "fas fa-check-circle",
        "fas fa-check-double",
        "fas fa-check-square",
        "fas fa-chess",
        "fas fa-chess-bishop",
        "fas fa-chess-board",
        "fas fa-chess-king",
        "fas fa-chess-knight",
        "fas fa-chess-pawn",
        "fas fa-chess-queen",
        "fas fa-chess-rook",
        "fas fa-chevron-circle-down",
        "fas fa-chevron-circle-left",
        "fas fa-chevron-circle-right",
        "fas fa-chevron-circle-up",
        "fas fa-chevron-down",
        "fas fa-chevron-left",
        "fas fa-chevron-right",
        "fas fa-chevron-up",
        "fas fa-child",
        "fas fa-church",
        "fas fa-circle",
        "fas fa-circle-notch",
        "fas fa-clipboard",
        "fas fa-clipboard-check",
        "fas fa-clipboard-list",
        "fas fa-clock",
        "fas fa-clone",
        "fas fa-closed-captioning",
        "fas fa-cloud",
        "fas fa-cloud-download-alt",
        "fas fa-cloud-upload-alt",
        "fas fa-cocktail",
        "fas fa-code",
        "fas fa-code-branch",
        "fas fa-coffee",
        "fas fa-cog",
        "fas fa-cogs",
        "fas fa-coins",
        "fas fa-columns",
        "fas fa-comment",
        "fas fa-comment-alt",
        "fas fa-comment-dots",
        "fas fa-comment-slash",
        "fas fa-comments",
        "fas fa-compact-disc",
        "fas fa-compass",
        "fas fa-compress",
        "fas fa-concierge-bell",
        "fas fa-cookie",
        "fas fa-cookie-bite",
        "fas fa-copy",
        "fas fa-copyright",
        "fas fa-couch",
        "fas fa-credit-card",
        "fas fa-crop",
        "fas fa-crop-alt",
        "fas fa-crosshairs",
        "fas fa-crow",
        "fas fa-crown",
        "fas fa-cube",
        "fas fa-cubes",
        "fas fa-cut",
        "fas fa-database",
        "fas fa-deaf",
        "fas fa-desktop",
        "fas fa-diagnoses",
        "fas fa-dice",
        "fas fa-dice-five",
        "fas fa-dice-four",
        "fas fa-dice-one",
        "fas fa-dice-six",
        "fas fa-dice-three",
        "fas fa-dice-two",
        "fas fa-digital-tachograph",
        "fas fa-directions",
        "fas fa-divide",
        "fas fa-dizzy",
        "fas fa-dna",
        "fas fa-dollar-sign",
        "fas fa-dolly",
        "fas fa-dolly-flatbed",
        "fas fa-donate",
        "fas fa-door-closed",
        "fas fa-door-open",
        "fas fa-dot-circle",
        "fas fa-dove",
        "fas fa-download",
        "fas fa-drafting-compass",
        "fas fa-draw-polygon",
        "fas fa-drum",
        "fas fa-drum-steelpan",
        "fas fa-dumbbell",
        "fas fa-edit",
        "fas fa-eject",
        "fas fa-ellipsis-h",
        "fas fa-ellipsis-v",
        "fas fa-envelope",
        "fas fa-envelope-open",
        "fas fa-envelope-square",
        "fas fa-equals",
        "fas fa-eraser",
        "fas fa-euro-sign",
        "fas fa-exchange-alt",
        "fas fa-exclamation",
        "fas fa-exclamation-circle",
        "fas fa-exclamation-triangle",
        "fas fa-expand",
        "fas fa-expand-arrows-alt",
        "fas fa-external-link-alt",
        "fas fa-external-link-square-alt",
        "fas fa-eye",
        "fas fa-eye-dropper",
        "fas fa-eye-slash",
        "fas fa-fast-backward",
        "fas fa-fast-forward",
        "fas fa-fax",
        "fas fa-feather",
        "fas fa-feather-alt",
        "fas fa-female",
        "fas fa-fighter-jet",
        "fas fa-file",
        "fas fa-file-alt",
        "fas fa-file-archive",
        "fas fa-file-audio",
        "fas fa-file-code",
        "fas fa-file-contract",
        "fas fa-file-download",
        "fas fa-file-excel",
        "fas fa-file-export",
        "fas fa-file-image",
        "fas fa-file-import",
        "fas fa-file-invoice",
        "fas fa-file-invoice-dollar",
        "fas fa-file-medical",
        "fas fa-file-medical-alt",
        "fas fa-file-pdf",
        "fas fa-file-powerpoint",
        "fas fa-file-prescription",
        "fas fa-file-signature",
        "fas fa-file-upload",
        "fas fa-file-video",
        "fas fa-file-word",
        "fas fa-fill",
        "fas fa-fill-drip",
        "fas fa-film",
        "fas fa-filter",
        "fas fa-fingerprint",
        "fas fa-fire",
        "fas fa-fire-extinguisher",
        "fas fa-first-aid",
        "fas fa-fish",
        "fas fa-flag",
        "fas fa-flag-checkered",
        "fas fa-flask",
        "fas fa-flushed",
        "fas fa-folder",
        "fas fa-folder-open",
        "fas fa-font",
        "fas fa-football-ball",
        "fas fa-forward",
        "fas fa-frog",
        "fas fa-frown",
        "fas fa-frown-open",
        "fas fa-futbol",
        "fas fa-gamepad",
        "fas fa-gas-pump",
        "fas fa-gavel",
        "fas fa-gem",
        "fas fa-genderless",
        "fas fa-gift",
        "fas fa-glass-martini",
        "fas fa-glass-martini-alt",
        "fas fa-glasses",
        "fas fa-globe",
        "fas fa-globe-africa",
        "fas fa-globe-americas",
        "fas fa-globe-asia",
        "fas fa-golf-ball",
        "fas fa-graduation-cap",
        "fas fa-greater-than",
        "fas fa-greater-than-equal",
        "fas fa-grimace",
        "fas fa-grin",
        "fas fa-grin-alt",
        "fas fa-grin-beam",
        "fas fa-grin-beam-sweat",
        "fas fa-grin-hearts",
        "fas fa-grin-squint",
        "fas fa-grin-squint-tears",
        "fas fa-grin-stars",
        "fas fa-grin-tears",
        "fas fa-grin-tongue",
        "fas fa-grin-tongue-squint",
        "fas fa-grin-tongue-wink",
        "fas fa-grin-wink",
        "fas fa-grip-horizontal",
        "fas fa-grip-vertical",
        "fas fa-h-square",
        "fas fa-hand-holding",
        "fas fa-hand-holding-heart",
        "fas fa-hand-holding-usd",
        "fas fa-hand-lizard",
        "fas fa-hand-paper",
        "fas fa-hand-peace",
        "fas fa-hand-point-down",
        "fas fa-hand-point-left",
        "fas fa-hand-point-right",
        "fas fa-hand-point-up",
        "fas fa-hand-pointer",
        "fas fa-hand-rock",
        "fas fa-hand-scissors",
        "fas fa-hand-spock",
        "fas fa-hands",
        "fas fa-hands-helping",
        "fas fa-handshake",
        "fas fa-hashtag",
        "fas fa-hdd",
        "fas fa-heading",
        "fas fa-headphones",
        "fas fa-headphones-alt",
        "fas fa-headset",
        "fas fa-heart",
        "fas fa-heartbeat",
        "fas fa-helicopter",
        "fas fa-highlighter",
        "fas fa-history",
        "fas fa-hockey-puck",
        "fas fa-home",
        "fas fa-hospital",
        "fas fa-hospital-alt",
        "fas fa-hospital-symbol",
        "fas fa-hot-tub",
        "fas fa-hotel",
        "fas fa-hourglass",
        "fas fa-hourglass-end",
        "fas fa-hourglass-half",
        "fas fa-hourglass-start",
        "fas fa-i-cursor",
        "fas fa-id-badge",
        "fas fa-id-card",
        "fas fa-id-card-alt",
        "fas fa-image",
        "fas fa-images",
        "fas fa-inbox",
        "fas fa-indent",
        "fas fa-industry",
        "fas fa-infinity",
        "fas fa-info",
        "fas fa-info-circle",
        "fas fa-italic",
        "fas fa-joint",
        "fas fa-key",
        "fas fa-keyboard",
        "fas fa-kiss",
        "fas fa-kiss-beam",
        "fas fa-kiss-wink-heart",
        "fas fa-kiwi-bird",
        "fas fa-language",
        "fas fa-laptop",
        "fas fa-laptop-code",
        "fas fa-laugh",
        "fas fa-laugh-beam",
        "fas fa-laugh-squint",
        "fas fa-laugh-wink",
        "fas fa-layer-group",
        "fas fa-leaf",
        "fas fa-lemon",
        "fas fa-less-than",
        "fas fa-less-than-equal",
        "fas fa-level-down-alt",
        "fas fa-level-up-alt",
        "fas fa-life-ring",
        "fas fa-lightbulb",
        "fas fa-link",
        "fas fa-lira-sign",
        "fas fa-list",
        "fas fa-list-alt",
        "fas fa-list-ol",
        "fas fa-list-ul",
        "fas fa-location-arrow",
        "fas fa-lock",
        "fas fa-lock-open",
        "fas fa-long-arrow-alt-down",
        "fas fa-long-arrow-alt-left",
        "fas fa-long-arrow-alt-right",
        "fas fa-long-arrow-alt-up",
        "fas fa-low-vision",
        "fas fa-luggage-cart",
        "fas fa-magic",
        "fas fa-magnet",
        "fas fa-male",
        "fas fa-map",
        "fas fa-map-marked",
        "fas fa-map-marked-alt",
        "fas fa-map-marker",
        "fas fa-map-marker-alt",
        "fas fa-map-pin",
        "fas fa-map-signs",
        "fas fa-marker",
        "fas fa-mars",
        "fas fa-mars-double",
        "fas fa-mars-stroke",
        "fas fa-mars-stroke-h",
        "fas fa-mars-stroke-v",
        "fas fa-medal",
        "fas fa-medkit",
        "fas fa-meh",
        "fas fa-meh-blank",
        "fas fa-meh-rolling-eyes",
        "fas fa-memory",
        "fas fa-mercury",
        "fas fa-microchip",
        "fas fa-microphone",
        "fas fa-microphone-alt",
        "fas fa-microphone-alt-slash",
        "fas fa-microphone-slash",
        "fas fa-microscope",
        "fas fa-minus",
        "fas fa-minus-circle",
        "fas fa-minus-square",
        "fas fa-mobile",
        "fas fa-mobile-alt",
        "fas fa-money-bill",
        "fas fa-money-bill-alt",
        "fas fa-money-bill-wave",
        "fas fa-money-bill-wave-alt",
        "fas fa-money-check",
        "fas fa-money-check-alt",
        "fas fa-monument",
        "fas fa-moon",
        "fas fa-mortar-pestle",
        "fas fa-motorcycle",
        "fas fa-mouse-pointer",
        "fas fa-music",
        "fas fa-neuter",
        "fas fa-newspaper",
        "fas fa-not-equal",
        "fas fa-notes-medical",
        "fas fa-object-group",
        "fas fa-object-ungroup",
        "fas fa-oil-can",
        "fas fa-outdent",
        "fas fa-paint-brush",
        "fas fa-paint-roller",
        "fas fa-palette",
        "fas fa-pallet",
        "fas fa-paper-plane",
        "fas fa-paperclip",
        "fas fa-parachute-box",
        "fas fa-paragraph",
        "fas fa-parking",
        "fas fa-passport",
        "fas fa-paste",
        "fas fa-pause",
        "fas fa-pause-circle",
        "fas fa-paw",
        "fas fa-pen",
        "fas fa-pen-alt",
        "fas fa-pen-fancy",
        "fas fa-pen-nib",
        "fas fa-pen-square",
        "fas fa-pencil-alt",
        "fas fa-pencil-ruler",
        "fas fa-people-carry",
        "fas fa-percent",
        "fas fa-percentage",
        "fas fa-phone",
        "fas fa-phone-slash",
        "fas fa-phone-square",
        "fas fa-phone-volume",
        "fas fa-piggy-bank",
        "fas fa-pills",
        "fas fa-plane",
        "fas fa-plane-arrival",
        "fas fa-plane-departure",
        "fas fa-play",
        "fas fa-play-circle",
        "fas fa-plug",
        "fas fa-plus",
        "fas fa-plus-circle",
        "fas fa-plus-square",
        "fas fa-podcast",
        "fas fa-poo",
        "fas fa-poop",
        "fas fa-portrait",
        "fas fa-pound-sign",
        "fas fa-power-off",
        "fas fa-prescription",
        "fas fa-prescription-bottle",
        "fas fa-prescription-bottle-alt",
        "fas fa-print",
        "fas fa-procedures",
        "fas fa-project-diagram",
        "fas fa-puzzle-piece",
        "fas fa-qrcode",
        "fas fa-question",
        "fas fa-question-circle",
        "fas fa-quidditch",
        "fas fa-quote-left",
        "fas fa-quote-right",
        "fas fa-random",
        "fas fa-receipt",
        "fas fa-recycle",
        "fas fa-redo",
        "fas fa-redo-alt",
        "fas fa-registered",
        "fas fa-reply",
        "fas fa-reply-all",
        "fas fa-retweet",
        "fas fa-ribbon",
        "fas fa-road",
        "fas fa-robot",
        "fas fa-rocket",
        "fas fa-route",
        "fas fa-rss",
        "fas fa-rss-square",
        "fas fa-ruble-sign",
        "fas fa-ruler",
        "fas fa-ruler-combined",
        "fas fa-ruler-horizontal",
        "fas fa-ruler-vertical",
        "fas fa-rupee-sign",
        "fas fa-sad-cry",
        "fas fa-sad-tear",
        "fas fa-save",
        "fas fa-school",
        "fas fa-screwdriver",
        "fas fa-search",
        "fas fa-search-minus",
        "fas fa-search-plus",
        "fas fa-seedling",
        "fas fa-server",
        "fas fa-shapes",
        "fas fa-share",
        "fas fa-share-alt",
        "fas fa-share-alt-square",
        "fas fa-share-square",
        "fas fa-shekel-sign",
        "fas fa-shield-alt",
        "fas fa-ship",
        "fas fa-shipping-fast",
        "fas fa-shoe-prints",
        "fas fa-shopping-bag",
        "fas fa-shopping-basket",
        "fas fa-shopping-cart",
        "fas fa-shower",
        "fas fa-shuttle-van",
        "fas fa-sign",
        "fas fa-sign-in-alt",
        "fas fa-sign-language",
        "fas fa-sign-out-alt",
        "fas fa-signal",
        "fas fa-signature",
        "fas fa-sitemap",
        "fas fa-skull",
        "fas fa-sliders-h",
        "fas fa-smile",
        "fas fa-smile-beam",
        "fas fa-smile-wink",
        "fas fa-smoking",
        "fas fa-smoking-ban",
        "fas fa-snowflake",
        "fas fa-solar-panel",
        "fas fa-sort",
        "fas fa-sort-alpha-down",
        "fas fa-sort-alpha-up",
        "fas fa-sort-amount-down",
        "fas fa-sort-amount-up",
        "fas fa-sort-down",
        "fas fa-sort-numeric-down",
        "fas fa-sort-numeric-up",
        "fas fa-sort-up",
        "fas fa-spa",
        "fas fa-space-shuttle",
        "fas fa-spinner",
        "fas fa-splotch",
        "fas fa-spray-can",
        "fas fa-square",
        "fas fa-square-full",
        "fas fa-stamp",
        "fas fa-star",
        "fas fa-star-half",
        "fas fa-star-half-alt",
        "fas fa-star-of-life",
        "fas fa-step-backward",
        "fas fa-step-forward",
        "fas fa-stethoscope",
        "fas fa-sticky-note",
        "fas fa-stop",
        "fas fa-stop-circle",
        "fas fa-stopwatch",
        "fas fa-store",
        "fas fa-store-alt",
        "fas fa-stream",
        "fas fa-street-view",
        "fas fa-strikethrough",
        "fas fa-stroopwafel",
        "fas fa-subscript",
        "fas fa-subway",
        "fas fa-suitcase",
        "fas fa-suitcase-rolling",
        "fas fa-sun",
        "fas fa-superscript",
        "fas fa-surprise",
        "fas fa-swatchbook",
        "fas fa-swimmer",
        "fas fa-swimming-pool",
        "fas fa-sync",
        "fas fa-sync-alt",
        "fas fa-syringe",
        "fas fa-table",
        "fas fa-table-tennis",
        "fas fa-tablet",
        "fas fa-tablet-alt",
        "fas fa-tablets",
        "fas fa-tachometer-alt",
        "fas fa-tag",
        "fas fa-tags",
        "fas fa-tape",
        "fas fa-tasks",
        "fas fa-taxi",
        "fas fa-teeth",
        "fas fa-teeth-open",
        "fas fa-terminal",
        "fas fa-text-height",
        "fas fa-text-width",
        "fas fa-th",
        "fas fa-th-large",
        "fas fa-th-list",
        "fas fa-theater-masks",
        "fas fa-thermometer",
        "fas fa-thermometer-empty",
        "fas fa-thermometer-full",
        "fas fa-thermometer-half",
        "fas fa-thermometer-quarter",
        "fas fa-thermometer-three-quarters",
        "fas fa-thumbs-down",
        "fas fa-thumbs-up",
        "fas fa-thumbtack",
        "fas fa-ticket-alt",
        "fas fa-times",
        "fas fa-times-circle",
        "fas fa-tint",
        "fas fa-tint-slash",
        "fas fa-tired",
        "fas fa-toggle-off",
        "fas fa-toggle-on",
        "fas fa-toolbox",
        "fas fa-tooth",
        "fas fa-trademark",
        "fas fa-traffic-light",
        "fas fa-train",
        "fas fa-transgender",
        "fas fa-transgender-alt",
        "fas fa-trash",
        "fas fa-trash-alt",
        "fas fa-tree",
        "fas fa-trophy",
        "fas fa-truck",
        "fas fa-truck-loading",
        "fas fa-truck-monster",
        "fas fa-truck-moving",
        "fas fa-truck-pickup",
        "fas fa-tshirt",
        "fas fa-tty",
        "fas fa-tv",
        "fas fa-umbrella",
        "fas fa-umbrella-beach",
        "fas fa-underline",
        "fas fa-undo",
        "fas fa-undo-alt",
        "fas fa-universal-access",
        "fas fa-university",
        "fas fa-unlink",
        "fas fa-unlock",
        "fas fa-unlock-alt",
        "fas fa-upload",
        "fas fa-user",
        "fas fa-user-alt",
        "fas fa-user-alt-slash",
        "fas fa-user-astronaut",
        "fas fa-user-check",
        "fas fa-user-circle",
        "fas fa-user-clock",
        "fas fa-user-cog",
        "fas fa-user-edit",
        "fas fa-user-friends",
        "fas fa-user-graduate",
        "fas fa-user-lock",
        "fas fa-user-md",
        "fas fa-user-minus",
        "fas fa-user-ninja",
        "fas fa-user-plus",
        "fas fa-user-secret",
        "fas fa-user-shield",
        "fas fa-user-slash",
        "fas fa-user-tag",
        "fas fa-user-tie",
        "fas fa-user-times",
        "fas fa-users",
        "fas fa-users-cog",
        "fas fa-utensil-spoon",
        "fas fa-utensils",
        "fas fa-vector-square",
        "fas fa-venus",
        "fas fa-venus-double",
        "fas fa-venus-mars",
        "fas fa-vial",
        "fas fa-vials",
        "fas fa-video",
        "fas fa-video-slash",
        "fas fa-volleyball-ball",
        "fas fa-volume-down",
        "fas fa-volume-off",
        "fas fa-volume-up",
        "fas fa-walking",
        "fas fa-wallet",
        "fas fa-warehouse",
        "fas fa-weight",
        "fas fa-weight-hanging",
        "fas fa-wheelchair",
        "fas fa-wifi",
        "fas fa-window-close",
        "fas fa-window-maximize",
        "fas fa-window-minimize",
        "fas fa-window-restore",
        "fas fa-wine-glass",
        "fas fa-wine-glass-alt",
        "fas fa-won-sign",
        "fas fa-wrench",
        "fas fa-x-ray",
        "fas fa-yen-sign",
        "far fa-address-book",
        "far fa-address-card",
        "far fa-angry",
        "far fa-arrow-alt-circle-down",
        "far fa-arrow-alt-circle-left",
        "far fa-arrow-alt-circle-right",
        "far fa-arrow-alt-circle-up",
        "far fa-bell",
        "far fa-bell-slash",
        "far fa-bookmark",
        "far fa-building",
        "far fa-calendar",
        "far fa-calendar-alt",
        "far fa-calendar-check",
        "far fa-calendar-minus",
        "far fa-calendar-plus",
        "far fa-calendar-times",
        "far fa-caret-square-down",
        "far fa-caret-square-left",
        "far fa-caret-square-right",
        "far fa-caret-square-up",
        "far fa-chart-bar",
        "far fa-check-circle",
        "far fa-check-square",
        "far fa-circle",
        "far fa-clipboard",
        "far fa-clock",
        "far fa-clone",
        "far fa-closed-captioning",
        "far fa-comment",
        "far fa-comment-alt",
        "far fa-comment-dots",
        "far fa-comments",
        "far fa-compass",
        "far fa-copy",
        "far fa-copyright",
        "far fa-credit-card",
        "far fa-dizzy",
        "far fa-dot-circle",
        "far fa-edit",
        "far fa-envelope",
        "far fa-envelope-open",
        "far fa-eye",
        "far fa-eye-slash",
        "far fa-file",
        "far fa-file-alt",
        "far fa-file-archive",
        "far fa-file-audio",
        "far fa-file-code",
        "far fa-file-excel",
        "far fa-file-image",
        "far fa-file-pdf",
        "far fa-file-powerpoint",
        "far fa-file-video",
        "far fa-file-word",
        "far fa-flag",
        "far fa-flushed",
        "far fa-folder",
        "far fa-folder-open",
        "far fa-frown",
        "far fa-frown-open",
        "far fa-futbol",
        "far fa-gem",
        "far fa-grimace",
        "far fa-grin",
        "far fa-grin-alt",
        "far fa-grin-beam",
        "far fa-grin-beam-sweat",
        "far fa-grin-hearts",
        "far fa-grin-squint",
        "far fa-grin-squint-tears",
        "far fa-grin-stars",
        "far fa-grin-tears",
        "far fa-grin-tongue",
        "far fa-grin-tongue-squint",
        "far fa-grin-tongue-wink",
        "far fa-grin-wink",
        "far fa-hand-lizard",
        "far fa-hand-paper",
        "far fa-hand-peace",
        "far fa-hand-point-down",
        "far fa-hand-point-left",
        "far fa-hand-point-right",
        "far fa-hand-point-up",
        "far fa-hand-pointer",
        "far fa-hand-rock",
        "far fa-hand-scissors",
        "far fa-hand-spock",
        "far fa-handshake",
        "far fa-hdd",
        "far fa-heart",
        "far fa-hospital",
        "far fa-hourglass",
        "far fa-id-badge",
        "far fa-id-card",
        "far fa-image",
        "far fa-images",
        "far fa-keyboard",
        "far fa-kiss",
        "far fa-kiss-beam",
        "far fa-kiss-wink-heart",
        "far fa-laugh",
        "far fa-laugh-beam",
        "far fa-laugh-squint",
        "far fa-laugh-wink",
        "far fa-lemon",
        "far fa-life-ring",
        "far fa-lightbulb",
        "far fa-list-alt",
        "far fa-map",
        "far fa-meh",
        "far fa-meh-blank",
        "far fa-meh-rolling-eyes",
        "far fa-minus-square",
        "far fa-money-bill-alt",
        "far fa-moon",
        "far fa-newspaper",
        "far fa-object-group",
        "far fa-object-ungroup",
        "far fa-paper-plane",
        "far fa-pause-circle",
        "far fa-play-circle",
        "far fa-plus-square",
        "far fa-question-circle",
        "far fa-registered",
        "far fa-sad-cry",
        "far fa-sad-tear",
        "far fa-save",
        "far fa-share-square",
        "far fa-smile",
        "far fa-smile-beam",
        "far fa-smile-wink",
        "far fa-snowflake",
        "far fa-square",
        "far fa-star",
        "far fa-star-half",
        "far fa-sticky-note",
        "far fa-stop-circle",
        "far fa-sun",
        "far fa-surprise",
        "far fa-thumbs-down",
        "far fa-thumbs-up",
        "far fa-times-circle",
        "far fa-tired",
        "far fa-trash-alt",
        "far fa-user",
        "far fa-user-circle",
        "far fa-window-close",
        "far fa-window-maximize",
        "far fa-window-minimize",
        "far fa-window-restore",
        "fab fa-500px",
        "fab fa-accessible-icon",
        "fab fa-accusoft",
        "fab fa-adn",
        "fab fa-adversal",
        "fab fa-affiliatetheme",
        "fab fa-algolia",
        "fab fa-amazon",
        "fab fa-amazon-pay",
        "fab fa-amilia",
        "fab fa-android",
        "fab fa-angellist",
        "fab fa-angrycreative",
        "fab fa-angular",
        "fab fa-app-store",
        "fab fa-app-store-ios",
        "fab fa-apper",
        "fab fa-apple",
        "fab fa-apple-pay",
        "fab fa-asymmetrik",
        "fab fa-audible",
        "fab fa-autoprefixer",
        "fab fa-avianex",
        "fab fa-aviato",
        "fab fa-aws",
        "fab fa-bandcamp",
        "fab fa-behance",
        "fab fa-behance-square",
        "fab fa-bimobject",
        "fab fa-bitbucket",
        "fab fa-bitcoin",
        "fab fa-bity",
        "fab fa-black-tie",
        "fab fa-blackberry",
        "fab fa-blogger",
        "fab fa-blogger-b",
        "fab fa-bluetooth",
        "fab fa-bluetooth-b",
        "fab fa-btc",
        "fab fa-buromobelexperte",
        "fab fa-buysellads",
        "fab fa-cc-amazon-pay",
        "fab fa-cc-amex",
        "fab fa-cc-apple-pay",
        "fab fa-cc-diners-club",
        "fab fa-cc-discover",
        "fab fa-cc-jcb",
        "fab fa-cc-mastercard",
        "fab fa-cc-paypal",
        "fab fa-cc-stripe",
        "fab fa-cc-visa",
        "fab fa-centercode",
        "fab fa-chrome",
        "fab fa-cloudscale",
        "fab fa-cloudsmith",
        "fab fa-cloudversify",
        "fab fa-codepen",
        "fab fa-codiepie",
        "fab fa-connectdevelop",
        "fab fa-contao",
        "fab fa-cpanel",
        "fab fa-creative-commons",
        "fab fa-creative-commons-by",
        "fab fa-creative-commons-nc",
        "fab fa-creative-commons-nc-eu",
        "fab fa-creative-commons-nc-jp",
        "fab fa-creative-commons-nd",
        "fab fa-creative-commons-pd",
        "fab fa-creative-commons-pd-alt",
        "fab fa-creative-commons-remix",
        "fab fa-creative-commons-sa",
        "fab fa-creative-commons-sampling",
        "fab fa-creative-commons-sampling-plus",
        "fab fa-creative-commons-share",
        "fab fa-css3",
        "fab fa-css3-alt",
        "fab fa-cuttlefish",
        "fab fa-d-and-d",
        "fab fa-dashcube",
        "fab fa-delicious",
        "fab fa-deploydog",
        "fab fa-deskpro",
        "fab fa-deviantart",
        "fab fa-digg",
        "fab fa-digital-ocean",
        "fab fa-discord",
        "fab fa-discourse",
        "fab fa-dochub",
        "fab fa-docker",
        "fab fa-draft2digital",
        "fab fa-dribbble",
        "fab fa-dribbble-square",
        "fab fa-dropbox",
        "fab fa-drupal",
        "fab fa-dyalog",
        "fab fa-earlybirds",
        "fab fa-ebay",
        "fab fa-edge",
        "fab fa-elementor",
        "fab fa-ello",
        "fab fa-ember",
        "fab fa-empire",
        "fab fa-envira",
        "fab fa-erlang",
        "fab fa-ethereum",
        "fab fa-etsy",
        "fab fa-expeditedssl",
        "fab fa-facebook",
        "fab fa-facebook-f",
        "fab fa-facebook-messenger",
        "fab fa-facebook-square",
        "fab fa-firefox",
        "fab fa-first-order",
        "fab fa-first-order-alt",
        "fab fa-firstdraft",
        "fab fa-flickr",
        "fab fa-flipboard",
        "fab fa-fly",
        "fab fa-font-awesome",
        "fab fa-font-awesome-alt",
        "fab fa-font-awesome-flag",
        "fab fa-fonticons",
        "fab fa-fonticons-fi",
        "fab fa-fort-awesome",
        "fab fa-fort-awesome-alt",
        "fab fa-forumbee",
        "fab fa-foursquare",
        "fab fa-free-code-camp",
        "fab fa-freebsd",
        "fab fa-fulcrum",
        "fab fa-galactic-republic",
        "fab fa-galactic-senate",
        "fab fa-get-pocket",
        "fab fa-gg",
        "fab fa-gg-circle",
        "fab fa-git",
        "fab fa-git-square",
        "fab fa-github",
        "fab fa-github-alt",
        "fab fa-github-square",
        "fab fa-gitkraken",
        "fab fa-gitlab",
        "fab fa-gitter",
        "fab fa-glide",
        "fab fa-glide-g",
        "fab fa-gofore",
        "fab fa-goodreads",
        "fab fa-goodreads-g",
        "fab fa-google",
        "fab fa-google-drive",
        "fab fa-google-play",
        "fab fa-google-plus",
        "fab fa-google-plus-g",
        "fab fa-google-plus-square",
        "fab fa-google-wallet",
        "fab fa-gratipay",
        "fab fa-grav",
        "fab fa-gripfire",
        "fab fa-grunt",
        "fab fa-gulp",
        "fab fa-hacker-news",
        "fab fa-hacker-news-square",
        "fab fa-hackerrank",
        "fab fa-hips",
        "fab fa-hire-a-helper",
        "fab fa-hooli",
        "fab fa-hornbill",
        "fab fa-hotjar",
        "fab fa-houzz",
        "fab fa-html5",
        "fab fa-hubspot",
        "fab fa-imdb",
        "fab fa-instagram",
        "fab fa-internet-explorer",
        "fab fa-ioxhost",
        "fab fa-itunes",
        "fab fa-itunes-note",
        "fab fa-java",
        "fab fa-jedi-order",
        "fab fa-jenkins",
        "fab fa-joget",
        "fab fa-joomla",
        "fab fa-js",
        "fab fa-js-square",
        "fab fa-jsfiddle",
        "fab fa-kaggle",
        "fab fa-keybase",
        "fab fa-keycdn",
        "fab fa-kickstarter",
        "fab fa-kickstarter-k",
        "fab fa-korvue",
        "fab fa-laravel",
        "fab fa-lastfm",
        "fab fa-lastfm-square",
        "fab fa-leanpub",
        "fab fa-less",
        "fab fa-line",
        "fab fa-linkedin",
        "fab fa-linkedin-in",
        "fab fa-linode",
        "fab fa-linux",
        "fab fa-lyft",
        "fab fa-magento",
        "fab fa-mailchimp",
        "fab fa-mandalorian",
        "fab fa-markdown",
        "fab fa-mastodon",
        "fab fa-maxcdn",
        "fab fa-medapps",
        "fab fa-medium",
        "fab fa-medium-m",
        "fab fa-medrt",
        "fab fa-meetup",
        "fab fa-megaport",
        "fab fa-microsoft",
        "fab fa-mix",
        "fab fa-mixcloud",
        "fab fa-mizuni",
        "fab fa-modx",
        "fab fa-monero",
        "fab fa-napster",
        "fab fa-neos",
        "fab fa-nimblr",
        "fab fa-nintendo-switch",
        "fab fa-node",
        "fab fa-node-js",
        "fab fa-npm",
        "fab fa-ns8",
        "fab fa-nutritionix",
        "fab fa-odnoklassniki",
        "fab fa-odnoklassniki-square",
        "fab fa-old-republic",
        "fab fa-opencart",
        "fab fa-openid",
        "fab fa-opera",
        "fab fa-optin-monster",
        "fab fa-osi",
        "fab fa-page4",
        "fab fa-pagelines",
        "fab fa-palfed",
        "fab fa-patreon",
        "fab fa-paypal",
        "fab fa-periscope",
        "fab fa-phabricator",
        "fab fa-phoenix-framework",
        "fab fa-phoenix-squadron",
        "fab fa-php",
        "fab fa-pied-piper",
        "fab fa-pied-piper-alt",
        "fab fa-pied-piper-hat",
        "fab fa-pied-piper-pp",
        "fab fa-pinterest",
        "fab fa-pinterest-p",
        "fab fa-pinterest-square",
        "fab fa-playstation",
        "fab fa-product-hunt",
        "fab fa-pushed",
        "fab fa-python",
        "fab fa-qq",
        "fab fa-quinscape",
        "fab fa-quora",
        "fab fa-r-project",
        "fab fa-ravelry",
        "fab fa-react",
        "fab fa-readme",
        "fab fa-rebel",
        "fab fa-red-river",
        "fab fa-reddit",
        "fab fa-reddit-alien",
        "fab fa-reddit-square",
        "fab fa-rendact",
        "fab fa-renren",
        "fab fa-replyd",
        "fab fa-researchgate",
        "fab fa-resolving",
        "fab fa-rev",
        "fab fa-rocketchat",
        "fab fa-rockrms",
        "fab fa-safari",
        "fab fa-sass",
        "fab fa-schlix",
        "fab fa-scribd",
        "fab fa-searchengin",
        "fab fa-sellcast",
        "fab fa-sellsy",
        "fab fa-servicestack",
        "fab fa-shirtsinbulk",
        "fab fa-shopware",
        "fab fa-simplybuilt",
        "fab fa-sistrix",
        "fab fa-sith",
        "fab fa-skyatlas",
        "fab fa-skype",
        "fab fa-slack",
        "fab fa-slack-hash",
        "fab fa-slideshare",
        "fab fa-snapchat",
        "fab fa-snapchat-ghost",
        "fab fa-snapchat-square",
        "fab fa-soundcloud",
        "fab fa-speakap",
        "fab fa-spotify",
        "fab fa-squarespace",
        "fab fa-stack-exchange",
        "fab fa-stack-overflow",
        "fab fa-staylinked",
        "fab fa-steam",
        "fab fa-steam-square",
        "fab fa-steam-symbol",
        "fab fa-sticker-mule",
        "fab fa-strava",
        "fab fa-stripe",
        "fab fa-stripe-s",
        "fab fa-studiovinari",
        "fab fa-stumbleupon",
        "fab fa-stumbleupon-circle",
        "fab fa-superpowers",
        "fab fa-supple",
        "fab fa-teamspeak",
        "fab fa-telegram",
        "fab fa-telegram-plane",
        "fab fa-tencent-weibo",
        "fab fa-themeco",
        "fab fa-themeisle",
        "fab fa-trade-federation",
        "fab fa-trello",
        "fab fa-tripadvisor",
        "fab fa-tumblr",
        "fab fa-tumblr-square",
        "fab fa-twitch",
        "fab fa-twitter",
        "fab fa-twitter-square",
        "fab fa-typo3",
        "fab fa-uber",
        "fab fa-uikit",
        "fab fa-uniregistry",
        "fab fa-untappd",
        "fab fa-usb",
        "fab fa-ussunnah",
        "fab fa-vaadin",
        "fab fa-viacoin",
        "fab fa-viadeo",
        "fab fa-viadeo-square",
        "fab fa-viber",
        "fab fa-vimeo",
        "fab fa-vimeo-square",
        "fab fa-vimeo-v",
        "fab fa-vine",
        "fab fa-vk",
        "fab fa-vnv",
        "fab fa-vuejs",
        "fab fa-weebly",
        "fab fa-weibo",
        "fab fa-weixin",
        "fab fa-whatsapp",
        "fab fa-whatsapp-square",
        "fab fa-whmcs",
        "fab fa-wikipedia-w",
        "fab fa-windows",
        "fab fa-wix",
        "fab fa-wolf-pack-battalion",
        "fab fa-wordpress",
        "fab fa-wordpress-simple",
        "fab fa-wpbeginner",
        "fab fa-wpexplorer",
        "fab fa-wpforms",
        "fab fa-xbox",
        "fab fa-xing",
        "fab fa-xing-square",
        "fab fa-y-combinator",
        "fab fa-yahoo",
        "fab fa-yandex",
        "fab fa-yandex-international",
        "fab fa-yelp",
        "fab fa-yoast",
        "fab fa-youtube",
        "fab fa-youtube-square",
        "fab fa-zhihu",
      ];
      var rowDemoIcon = "#row-demo-icon";
      for (i = 0; i < iconClass.length; i++) {
        $(rowDemoIcon).append(
          '<div class="col-md-3"> <div class="demo-icon"> <div class="icon-preview"><i class="' +
            iconClass[i] +
            '"></i></div> <div class="icon-class">' +
            iconClass[i] +
            "</div> </div> </div>"
        );
      }
    </script>
  </body>
</html>
