

<?php $__env->startSection('title', 'DAFTAR ULASAN PRODUK | RFF'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container">
        <div class="page-inner">
            <!-- Data Tables -->
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-12">
                        <!-- Title Section -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3 class="fw-bold">Manajemen Ulasan Produk</h3>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('akses_tambah_ulasan')): ?>
                                <!-- Button to add a new review -->
                                <a href="<?php echo e(route('dashboard.reviews.create')); ?>" class="btn btn-success">
                                    <i class="fas fa-plus"></i> <PERSON><PERSON>
                                </a>
                            <?php endif; ?>
                        </div>

                        <!-- Menampilkan pesan sukses atau gagal -->
                        <?php if(session('success')): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo e(session('success')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert"
                                    aria-label="Close"></button>
                            </div>
                        <?php elseif(session('error')): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo e(session('error')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert"
                                    aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Table -->
                        <div class="card">
                            <div class="card-header">
                                <h4 class="text-center mt-3">Daftar Ulasan Produk</h4>
                            </div>
                            <div class="card-body">
                                <table id="dataTable" class="table table-striped table-bordered" style="width: 100%">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>ID Produk</th>
                                            <th>Nama Produk</th>
                                            <th>Rating</th>
                                            <th>Ulasan</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($loop->iteration); ?></td>
                                                <td><?php echo e('PRD-' . str_pad($review->product->id, 3, '0', STR_PAD_LEFT)); ?></td>
                                                <td><?php echo e($review->product->nama); ?></td>
                                                <td><?php echo e($review->rating); ?></td>
                                                <td><?php echo e($review->ulasan); ?></td>
                                                <td>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('akses_edit_ulasan')): ?>
                                                        <a href="<?php echo e(route('dashboard.reviews.edit', $review->id)); ?>"
                                                            class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </a>
                                                    <?php endif; ?>

                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('akses_tampil_ulasan')): ?>
                                                        <a href="<?php echo e(route('dashboard.reviews.show', $review->id)); ?>"
                                                            class="btn btn-sm btn-primary">
                                                            <i class="fas fa-eye"></i> Detail
                                                        </a>
                                                    <?php endif; ?>

                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('akses_hapus_ulasan')): ?>
                                                        <form action="<?php echo e(route('dashboard.reviews.destroy', $review->id)); ?>"
                                                            method="POST" style="display:inline-block;">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="btn btn-sm btn-danger"
                                                                onclick="return confirm('Hapus ulasan ini?')">
                                                                <i class="fas fa-trash"></i> Hapus
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('includes.admin.myScripts.reviews.indexScripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Complied\web pi\webtest1\resources\views/dashboard/admin/reviews/index.blade.php ENDPATH**/ ?>