

<?php $__env->startSection('title', 'DAFTAR PRODUK | RFF'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container">
        <div class="page-inner">
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-12">
                        <!-- Title Section -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3 class="fw-bold">Manajemen Produk</h3>
                        </div>


                        <!-- Menampilkan pesan sukses atau gagal -->
                        <?php if(session('success')): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo e(session('success')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php elseif(session('error')): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo e(session('error')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert"
                                    aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Table -->
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-start align-items-center">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('akses_tambah_produk')): ?>
                                        <!-- Button to add a new product -->
                                        <a href="<?php echo e(route('dashboard.products.create')); ?>" class="btn btn-success">
                                            <i class="fas fa-plus"></i> Tambah Produk
                                        </a>
                                    <?php endif; ?>
                                </div>

                                <h4 class="text-center mt-3">Daftar Produk</h4>
                            </div>
                            <div class="card-body">
                                <table id="dataTable" class="table table-striped table-bordered" style="width: 100%">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>ID</th>
                                            <th>Nama</th>
                                            <th>Gambar</th>
                                            <th>Stok</th>
                                            <th>Harga</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <tr>
                                                <td><?php echo e($key + 1); ?></td>
                                                <td><?php echo e('PRD-' . str_pad($product->id, 3, '0', STR_PAD_LEFT)); ?></td>
                                                <td><?php echo e($product->nama); ?></td>
                                                <td>
                                                    <?php if($product->gambar_produk): ?>
                                                        <img src="<?php echo e(asset('storage/uploads/produk/' . $product->gambar_produk)); ?>" alt="Product Image" style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px; margin: 5px;">
                                                    <?php else: ?>
                                                        <span class="text-muted">Tidak ada gambar</span>
                                                    <?php endif; ?>
                                                </td>

                                                <td><?php echo e($product->stok); ?></td>
                                                <td>Rp <?php echo e(number_format($product->harga, 0, ',', '.')); ?></td>
                                                <td>
                                                    <?php if(auth()->user()->role_id == 1): ?>
                                                        <a href="<?php echo e(route('dashboard.products.edit', $product->id)); ?>"
                                                            class="btn btn-sm btn-warning"
                                                            style="padding: 5px 10px; border-radius: 5px; margin-right: 5px;">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </a>
                                                    <?php endif; ?>

                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('akses_tampil_produk')): ?>
                                                        <a href="<?php echo e(route('dashboard.products.show', $product->id)); ?>"
                                                            class="btn btn-sm btn-primary"
                                                            style="padding: 5px 10px; border-radius: 5px; margin-right: 5px;">
                                                            <i class="fas fa-eye"></i> Detail
                                                        </a>
                                                    <?php endif; ?>

                                                    <?php if(auth()->user()->role_id == 1): ?>
                                                        <form action="<?php echo e(route('dashboard.products.destroy', $product->id)); ?>"
                                                            method="POST" style="display:inline-block;">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="btn btn-sm btn-danger"
                                                                style="padding: 5px 10px; border-radius: 5px;"
                                                                onclick="return confirm('Hapus produk ini?')">
                                                                <i class="fas fa-trash"></i> Hapus
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                            <tr>
                                                <td colspan="7" class="text-center">Tidak ada produk</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('includes.admin.myScripts.produk.indexScripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Complied\web pi\webtest1\resources\views/dashboard/admin/produk/index.blade.php ENDPATH**/ ?>