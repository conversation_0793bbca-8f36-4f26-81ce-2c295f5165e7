<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        // Image preview functionality
        $('img[alt="<PERSON><PERSON><PERSON>"]').on('click', function() {
            const imgSrc = $(this).attr('src');
            Swal.fire({
                imageUrl: imgSrc,
                imageAlt: '<PERSON><PERSON><PERSON>',
                showConfirmButton: false,
                background: 'rgba(0,0,0,0.8)',
                backdrop: `
                    rgba(0,0,0,0.7)
                    url("${imgSrc}")
                    center top
                    no-repeat
                `,
                showCloseButton: true,
                width: '80%'
            });
        });

        // Edit confirmation
        $('a[href*="/edit"]').on('click', function(e) {
            e.preventDefault();
            const href = $(this).attr('href');
            
            Swal.fire({
                title: 'Konfirmasi Edit',
                text: 'Anda yakin ingin mengedit transaksi ini?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Ya, Edit',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = href;
                }
            });
        });
    });
</script><?php /**PATH C:\Users\<USER>\Desktop\Complied\web pi\webtest1\resources\views/includes/admin/myScripts/transaksi/showScripts.blade.php ENDPATH**/ ?>